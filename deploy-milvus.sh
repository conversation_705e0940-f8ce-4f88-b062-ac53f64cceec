#!/bin/bash

# Milvus向量数据库部署脚本
# 作者: ruoyi
# 日期: 2024-08-13

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 检查Docker是否安装
check_docker() {
    log_step "检查Docker环境..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose未安装，请先安装Docker Compose"
        exit 1
    fi
    
    log_info "Docker环境检查通过"
}

# 创建目录结构
create_directories() {
    log_step "创建数据目录..."
    
    mkdir -p volumes/etcd
    mkdir -p volumes/minio
    mkdir -p volumes/milvus
    
    log_info "数据目录创建完成"
}

# 创建Docker Compose文件
create_docker_compose() {
    log_step "创建Docker Compose配置文件..."
    
    cat > docker-compose-milvus.yml << 'EOF'
version: '3.5'

services:
  etcd:
    container_name: milvus-etcd
    image: quay.io/coreos/etcd:v3.5.5
    environment:
      - ETCD_AUTO_COMPACTION_MODE=revision
      - ETCD_AUTO_COMPACTION_RETENTION=1000
      - ETCD_QUOTA_BACKEND_BYTES=**********
      - ETCD_SNAPSHOT_COUNT=50000
    volumes:
      - ${DOCKER_VOLUME_DIRECTORY:-.}/volumes/etcd:/etcd
    command: etcd -advertise-client-urls=http://127.0.0.1:2379 -listen-client-urls http://0.0.0.0:2379 --data-dir /etcd
    healthcheck:
      test: ["CMD", "etcdctl", "endpoint", "health"]
      interval: 30s
      timeout: 20s
      retries: 3
    networks:
      - milvus

  minio:
    container_name: milvus-minio
    image: minio/minio:RELEASE.2023-03-20T20-16-18Z
    environment:
      MINIO_ACCESS_KEY: minioadmin
      MINIO_SECRET_KEY: minioadmin
    ports:
      - "9001:9001"
      - "9000:9000"
    volumes:
      - ${DOCKER_VOLUME_DIRECTORY:-.}/volumes/minio:/minio_data
    command: minio server /minio_data --console-address ":9001"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3
    networks:
      - milvus

  standalone:
    container_name: milvus-standalone
    image: milvusdb/milvus:v2.4.5
    command: ["milvus", "run", "standalone"]
    security_opt:
    - seccomp:unconfined
    environment:
      ETCD_ENDPOINTS: etcd:2379
      MINIO_ADDRESS: minio:9000
    volumes:
      - ${DOCKER_VOLUME_DIRECTORY:-.}/volumes/milvus:/var/lib/milvus
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9091/healthz"]
      interval: 30s
      start_period: 90s
      timeout: 20s
      retries: 3
    ports:
      - "19530:19530"
      - "9091:9091"
    depends_on:
      - "etcd"
      - "minio"
    networks:
      - milvus

networks:
  milvus:
    driver: bridge
EOF
    
    log_info "Docker Compose配置文件创建完成"
}

# 启动Milvus服务
start_milvus() {
    log_step "启动Milvus服务..."
    
    docker-compose -f docker-compose-milvus.yml up -d
    
    log_info "Milvus服务启动中，请等待健康检查完成..."
    
    # 等待服务启动
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if docker-compose -f docker-compose-milvus.yml ps | grep -q "healthy"; then
            log_info "Milvus服务启动成功！"
            return 0
        fi
        
        log_warn "等待服务启动... (${attempt}/${max_attempts})"
        sleep 10
        ((attempt++))
    done
    
    log_error "Milvus服务启动超时，请检查日志"
    docker-compose -f docker-compose-milvus.yml logs
    exit 1
}

# 验证服务状态
verify_services() {
    log_step "验证服务状态..."
    
    # 检查Milvus连接
    if curl -s -f http://localhost:9091/healthz > /dev/null; then
        log_info "✓ Milvus服务运行正常"
    else
        log_error "✗ Milvus服务连接失败"
        return 1
    fi
    
    # 检查MinIO
    if curl -s -f http://localhost:9000/minio/health/live > /dev/null; then
        log_info "✓ MinIO服务运行正常"
    else
        log_error "✗ MinIO服务连接失败"
        return 1
    fi
    
    log_info "所有服务验证通过！"
}

# 显示服务信息
show_service_info() {
    log_step "服务信息"
    
    echo ""
    echo "🎉 Milvus向量数据库部署完成！"
    echo ""
    echo "📊 服务访问地址："
    echo "   Milvus服务: localhost:19530"
    echo "   Milvus健康检查: http://localhost:9091/healthz"
    echo "   MinIO控制台: http://localhost:9001 (minioadmin/minioadmin)"
    echo "   MinIO API: http://localhost:9000"
    echo ""
    echo "📁 数据存储目录："
    echo "   $(pwd)/volumes/milvus"
    echo "   $(pwd)/volumes/minio"
    echo "   $(pwd)/volumes/etcd"
    echo ""
    echo "🔧 管理命令："
    echo "   查看服务状态: docker-compose -f docker-compose-milvus.yml ps"
    echo "   查看服务日志: docker-compose -f docker-compose-milvus.yml logs"
    echo "   停止服务: docker-compose -f docker-compose-milvus.yml down"
    echo "   重启服务: docker-compose -f docker-compose-milvus.yml restart"
    echo ""
    echo "📖 接下来的步骤："
    echo "   1. 修改应用配置文件中的存储类型为 'milvus'"
    echo "   2. 重启应用服务"
    echo "   3. 通过 /knowledge/milvus/test/status 接口验证集成"
    echo ""
}

# 停止服务
stop_milvus() {
    log_step "停止Milvus服务..."
    
    if [ -f "docker-compose-milvus.yml" ]; then
        docker-compose -f docker-compose-milvus.yml down
        log_info "Milvus服务已停止"
    else
        log_warn "Docker Compose文件不存在"
    fi
}

# 清理数据
clean_data() {
    log_step "清理Milvus数据..."
    
    read -p "确定要删除所有Milvus数据吗？这将无法恢复！(y/N): " -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        stop_milvus
        rm -rf volumes/
        rm -f docker-compose-milvus.yml
        log_info "数据清理完成"
    else
        log_info "取消清理操作"
    fi
}

# 显示帮助信息
show_help() {
    echo "Milvus向量数据库部署脚本"
    echo ""
    echo "用法: $0 [命令]"
    echo ""
    echo "命令:"
    echo "  start    启动Milvus服务（默认）"
    echo "  stop     停止Milvus服务"
    echo "  restart  重启Milvus服务"
    echo "  status   查看服务状态"
    echo "  logs     查看服务日志"
    echo "  clean    清理所有数据"
    echo "  help     显示帮助信息"
    echo ""
}

# 主函数
main() {
    local command=${1:-start}
    
    case $command in
        start)
            check_docker
            create_directories
            create_docker_compose
            start_milvus
            verify_services
            show_service_info
            ;;
        stop)
            stop_milvus
            ;;
        restart)
            stop_milvus
            sleep 5
            start_milvus
            verify_services
            ;;
        status)
            if [ -f "docker-compose-milvus.yml" ]; then
                docker-compose -f docker-compose-milvus.yml ps
            else
                log_error "Milvus服务未部署"
            fi
            ;;
        logs)
            if [ -f "docker-compose-milvus.yml" ]; then
                docker-compose -f docker-compose-milvus.yml logs -f
            else
                log_error "Milvus服务未部署"
            fi
            ;;
        clean)
            clean_data
            ;;
        help)
            show_help
            ;;
        *)
            log_error "未知命令: $command"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
