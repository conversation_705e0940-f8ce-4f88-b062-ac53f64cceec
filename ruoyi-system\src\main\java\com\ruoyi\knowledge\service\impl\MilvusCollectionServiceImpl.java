package com.ruoyi.knowledge.service.impl;

import com.ruoyi.knowledge.config.MilvusConfig;
import com.ruoyi.knowledge.service.IMilvusCollectionService;
import io.milvus.client.MilvusServiceClient;
import io.milvus.param.ConnectParam;
import io.milvus.param.R;
import io.milvus.param.collection.*;
import io.milvus.param.index.CreateIndexParam;
import io.milvus.param.index.DropIndexParam;
import io.milvus.grpc.DataType;
import io.milvus.grpc.GetCollectionStatisticsResponse;
import io.milvus.param.dml.FlushParam;
import io.milvus.param.control.CompactParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.Arrays;

/**
 * Milvus集合管理服务实现
 * 
 * <AUTHOR>
 * @date 2024-08-13
 */
@Service
public class MilvusCollectionServiceImpl implements IMilvusCollectionService {

    private static final Logger logger = LoggerFactory.getLogger(MilvusCollectionServiceImpl.class);

    @Autowired
    private MilvusConfig milvusConfig;

    private MilvusServiceClient milvusClient;

    @PostConstruct
    public void init() {
        try {
            ConnectParam.Builder connectBuilder = ConnectParam.newBuilder()
                    .withHost(milvusConfig.getHost())
                    .withPort(milvusConfig.getPort())
                    .withConnectTimeout(milvusConfig.getConnectTimeoutMs())
                    .withKeepAliveTime(milvusConfig.getKeepAliveTimeMs())
                    .withKeepAliveTimeout(milvusConfig.getKeepAliveTimeoutMs())
                    .withKeepAliveWithoutCalls(milvusConfig.isKeepAliveWithoutCalls())
                    .withSecure(milvusConfig.isSecure())
                    .withIdleTimeout(30000L);

            // 如果需要认证
            if (milvusConfig.needsAuth()) {
                connectBuilder.withUsername(milvusConfig.getUsername())
                        .withPassword(milvusConfig.getPassword());
            }

            // 如果指定了数据库
            if (milvusConfig.getDatabase() != null && !milvusConfig.getDatabase().equals("default")) {
                connectBuilder.withDatabaseName(milvusConfig.getDatabase());
            }

            milvusClient = new MilvusServiceClient(connectBuilder.build());
            logger.info("Milvus客户端连接成功: {}:{}", milvusConfig.getHost(), milvusConfig.getPort());
        } catch (Exception e) {
            logger.error("初始化Milvus客户端失败: {}", e.getMessage(), e);
        }
    }

    @PreDestroy
    public void destroy() {
        if (milvusClient != null) {
            try {
                milvusClient.close();
                logger.info("Milvus客户端连接已关闭");
            } catch (Exception e) {
                logger.error("关闭Milvus客户端失败: {}", e.getMessage(), e);
            }
        }
    }

    @Override
    public boolean createCollection(Long knowledgeBaseId, int dimension) {
        if (milvusClient == null) {
            logger.error("Milvus客户端未初始化");
            return false;
        }

        String collectionName = milvusConfig.getCollectionName(knowledgeBaseId);

        try {
            // 检查集合是否已存在
            if (hasCollection(knowledgeBaseId)) {
                logger.info("集合 {} 已存在", collectionName);
                return true;
            }

            // 创建字段
            FieldType idField = FieldType.newBuilder()
                    .withName("id")
                    .withDataType(DataType.Int64)
                    .withPrimaryKey(true)
                    .withAutoID(true)
                    .build();

            FieldType vectorField = FieldType.newBuilder()
                    .withName("vector")
                    .withDataType(DataType.FloatVector)
                    .withDimension(dimension)
                    .build();

            FieldType textField = FieldType.newBuilder()
                    .withName("text")
                    .withDataType(DataType.VarChar)
                    .withMaxLength(65535)
                    .build();

            FieldType metadataField = FieldType.newBuilder()
                    .withName("metadata")
                    .withDataType(DataType.JSON)
                    .build();

            // 创建集合
            CreateCollectionParam createCollectionParam = CreateCollectionParam.newBuilder()
                    .withCollectionName(collectionName)
                    .withDescription("知识库 " + knowledgeBaseId + " 的向量集合")
                    .withShardsNum(milvusConfig.getShardsNum())
                    .addFieldType(idField)
                    .addFieldType(vectorField)
                    .addFieldType(textField)
                    .addFieldType(metadataField)
                    .build();

            R<RpcStatus> response = milvusClient.createCollection(createCollectionParam);

            if (response.getStatus() == R.Status.Success.getCode()) {
                logger.info("成功创建集合: {}", collectionName);

                // 如果配置了自动创建索引，则创建索引
                if (milvusConfig.isAutoCreateIndex()) {
                    createIndex(knowledgeBaseId);
                }

                // 如果配置了自动加载集合，则加载集合
                if (milvusConfig.isAutoLoadCollection()) {
                    loadCollection(knowledgeBaseId);
                }

                return true;
            } else {
                logger.error("创建集合失败: {}, 错误: {}", collectionName, response.getMessage());
                return false;
            }
        } catch (Exception e) {
            logger.error("创建集合 {} 时发生异常: {}", collectionName, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean dropCollection(Long knowledgeBaseId) {
        if (milvusClient == null) {
            logger.error("Milvus客户端未初始化");
            return false;
        }

        String collectionName = milvusConfig.getCollectionName(knowledgeBaseId);

        try {
            DropCollectionParam dropCollectionParam = DropCollectionParam.newBuilder()
                    .withCollectionName(collectionName)
                    .build();

            R<RpcStatus> response = milvusClient.dropCollection(dropCollectionParam);

            if (response.getStatus() == R.Status.Success.getCode()) {
                logger.info("成功删除集合: {}", collectionName);
                return true;
            } else {
                logger.error("删除集合失败: {}, 错误: {}", collectionName, response.getMessage());
                return false;
            }
        } catch (Exception e) {
            logger.error("删除集合 {} 时发生异常: {}", collectionName, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean hasCollection(Long knowledgeBaseId) {
        if (milvusClient == null) {
            logger.error("Milvus客户端未初始化");
            return false;
        }

        String collectionName = milvusConfig.getCollectionName(knowledgeBaseId);

        try {
            HasCollectionParam hasCollectionParam = HasCollectionParam.newBuilder()
                    .withCollectionName(collectionName)
                    .build();

            R<Boolean> response = milvusClient.hasCollection(hasCollectionParam);

            if (response.getStatus() == R.Status.Success.getCode()) {
                return response.getData();
            } else {
                logger.error("检查集合存在性失败: {}, 错误: {}", collectionName, response.getMessage());
                return false;
            }
        } catch (Exception e) {
            logger.error("检查集合 {} 存在性时发生异常: {}", collectionName, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean loadCollection(Long knowledgeBaseId) {
        if (milvusClient == null) {
            logger.error("Milvus客户端未初始化");
            return false;
        }

        String collectionName = milvusConfig.getCollectionName(knowledgeBaseId);

        try {
            LoadCollectionParam loadCollectionParam = LoadCollectionParam.newBuilder()
                    .withCollectionName(collectionName)
                    .build();

            R<RpcStatus> response = milvusClient.loadCollection(loadCollectionParam);

            if (response.getStatus() == R.Status.Success.getCode()) {
                logger.info("成功加载集合: {}", collectionName);
                return true;
            } else {
                logger.error("加载集合失败: {}, 错误: {}", collectionName, response.getMessage());
                return false;
            }
        } catch (Exception e) {
            logger.error("加载集合 {} 时发生异常: {}", collectionName, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean releaseCollection(Long knowledgeBaseId) {
        if (milvusClient == null) {
            logger.error("Milvus客户端未初始化");
            return false;
        }

        String collectionName = milvusConfig.getCollectionName(knowledgeBaseId);

        try {
            ReleaseCollectionParam releaseCollectionParam = ReleaseCollectionParam.newBuilder()
                    .withCollectionName(collectionName)
                    .build();

            R<RpcStatus> response = milvusClient.releaseCollection(releaseCollectionParam);

            if (response.getStatus() == R.Status.Success.getCode()) {
                logger.info("成功释放集合: {}", collectionName);
                return true;
            } else {
                logger.error("释放集合失败: {}, 错误: {}", collectionName, response.getMessage());
                return false;
            }
        } catch (Exception e) {
            logger.error("释放集合 {} 时发生异常: {}", collectionName, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean createIndex(Long knowledgeBaseId) {
        if (milvusClient == null) {
            logger.error("Milvus客户端未初始化");
            return false;
        }

        String collectionName = milvusConfig.getCollectionName(knowledgeBaseId);

        try {
            CreateIndexParam createIndexParam = CreateIndexParam.newBuilder()
                    .withCollectionName(collectionName)
                    .withFieldName("vector")
                    .withIndexType(io.milvus.grpc.IndexType.valueOf(milvusConfig.getDefaultIndexType()))
                    .withMetricType(io.milvus.grpc.MetricType.valueOf(milvusConfig.getDefaultMetricType()))
                    .withExtraParam("{\"nlist\":" + milvusConfig.getIndexNlist() + "}")
                    .withSyncMode(Boolean.TRUE)
                    .build();

            R<RpcStatus> response = milvusClient.createIndex(createIndexParam);

            if (response.getStatus() == R.Status.Success.getCode()) {
                logger.info("成功创建索引: {}", collectionName);
                return true;
            } else {
                logger.error("创建索引失败: {}, 错误: {}", collectionName, response.getMessage());
                return false;
            }
        } catch (Exception e) {
            logger.error("创建索引 {} 时发生异常: {}", collectionName, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean dropIndex(Long knowledgeBaseId) {
        if (milvusClient == null) {
            logger.error("Milvus客户端未初始化");
            return false;
        }

        String collectionName = milvusConfig.getCollectionName(knowledgeBaseId);

        try {
            DropIndexParam dropIndexParam = DropIndexParam.newBuilder()
                    .withCollectionName(collectionName)
                    .withFieldName("vector")
                    .build();

            R<RpcStatus> response = milvusClient.dropIndex(dropIndexParam);

            if (response.getStatus() == R.Status.Success.getCode()) {
                logger.info("成功删除索引: {}", collectionName);
                return true;
            } else {
                logger.error("删除索引失败: {}, 错误: {}", collectionName, response.getMessage());
                return false;
            }
        } catch (Exception e) {
            logger.error("删除索引 {} 时发生异常: {}", collectionName, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public CollectionStats getCollectionStats(Long knowledgeBaseId) {
        if (milvusClient == null) {
            logger.error("Milvus客户端未初始化");
            return new CollectionStats();
        }

        String collectionName = milvusConfig.getCollectionName(knowledgeBaseId);

        try {
            GetCollectionStatisticsParam getStatsParam = GetCollectionStatisticsParam.newBuilder()
                    .withCollectionName(collectionName)
                    .build();

            R<GetCollectionStatisticsResponse> response = milvusClient.getCollectionStatistics(getStatsParam);

            if (response.getStatus() == R.Status.Success.getCode()) {
                GetCollectionStatisticsResponse stats = response.getData();

                // 解析统计信息
                long entityCount = 0;
                for (io.milvus.grpc.KeyValuePair pair : stats.getStatsList()) {
                    if ("row_count".equals(pair.getKey())) {
                        try {
                            entityCount = Long.parseLong(pair.getValue());
                        } catch (NumberFormatException e) {
                            logger.warn("解析实体数量失败: {}", pair.getValue());
                        }
                    }
                }

                return new CollectionStats(entityCount, entityCount, "loaded", 0, 0);
            } else {
                logger.error("获取集合统计信息失败: {}, 错误: {}", collectionName, response.getMessage());
                return new CollectionStats();
            }
        } catch (Exception e) {
            logger.error("获取集合 {} 统计信息时发生异常: {}", collectionName, e.getMessage(), e);
            return new CollectionStats();
        }
    }

    @Override
    public boolean flushCollection(Long knowledgeBaseId) {
        if (milvusClient == null) {
            logger.error("Milvus客户端未初始化");
            return false;
        }

        String collectionName = milvusConfig.getCollectionName(knowledgeBaseId);

        try {
            FlushParam flushParam = FlushParam.newBuilder()
                    .withCollectionNames(Arrays.asList(collectionName))
                    .withSyncFlush(true)
                    .build();

            R<FlushResponse> response = milvusClient.flush(flushParam);

            if (response.getStatus() == R.Status.Success.getCode()) {
                logger.info("成功刷新集合: {}", collectionName);
                return true;
            } else {
                logger.error("刷新集合失败: {}, 错误: {}", collectionName, response.getMessage());
                return false;
            }
        } catch (Exception e) {
            logger.error("刷新集合 {} 时发生异常: {}", collectionName, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean compactCollection(Long knowledgeBaseId) {
        if (milvusClient == null) {
            logger.error("Milvus客户端未初始化");
            return false;
        }

        String collectionName = milvusConfig.getCollectionName(knowledgeBaseId);

        try {
            CompactParam compactParam = CompactParam.newBuilder()
                    .withCollectionName(collectionName)
                    .build();

            R<ManualCompactionResponse> response = milvusClient.manualCompact(compactParam);

            if (response.getStatus() == R.Status.Success.getCode()) {
                logger.info("成功压缩集合: {}", collectionName);
                return true;
            } else {
                logger.error("压缩集合失败: {}, 错误: {}", collectionName, response.getMessage());
                return false;
            }
        } catch (Exception e) {
            logger.error("压缩集合 {} 时发生异常: {}", collectionName, e.getMessage(), e);
            return false;
        }
    }
}
