package com.ruoyi.knowledge;

import com.ruoyi.knowledge.config.MilvusConfig;
import com.ruoyi.knowledge.service.IMilvusCollectionService;
import com.ruoyi.knowledge.store.CustomMilvusEmbeddingStore;
import dev.langchain4j.data.embedding.Embedding;
import dev.langchain4j.data.segment.TextSegment;
import dev.langchain4j.model.embedding.EmbeddingModel;
import dev.langchain4j.model.embedding.onnx.allminilml6v2.AllMiniLmL6V2EmbeddingModel;
import dev.langchain4j.store.embedding.EmbeddingSearchRequest;
import dev.langchain4j.store.embedding.EmbeddingSearchResult;
import dev.langchain4j.store.embedding.EmbeddingMatch;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Order;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Milvus集成测试
 * 
 * <AUTHOR>
 * @date 2024-08-13
 */
@SpringBootTest
@ActiveProfiles("test")
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class MilvusIntegrationTest {

    private static final Logger logger = LoggerFactory.getLogger(MilvusIntegrationTest.class);

    @Autowired(required = false)
    private MilvusConfig milvusConfig;

    @Autowired(required = false)
    private IMilvusCollectionService milvusCollectionService;

    @Autowired(required = false)
    private CustomMilvusEmbeddingStore customMilvusEmbeddingStore;

    private EmbeddingModel embeddingModel;
    private Long testKnowledgeBaseId = 999L;

    @BeforeEach
    public void setUp() {
        embeddingModel = new AllMiniLmL6V2EmbeddingModel();
        logger.info("测试环境初始化完成");
    }

    @Test
    @Order(1)
    public void testMilvusConfigLoaded() {
        if (milvusConfig == null) {
            logger.warn("Milvus配置未加载，跳过测试");
            return;
        }
        
        assertNotNull(milvusConfig, "Milvus配置应该被正确加载");
        assertEquals("localhost", milvusConfig.getHost(), "默认主机应该是localhost");
        assertEquals(19530, milvusConfig.getPort(), "默认端口应该是19530");
        assertEquals(384, milvusConfig.getDefaultDimension(), "默认维度应该是384");
        
        logger.info("Milvus配置测试通过: {}", milvusConfig.getUri());
    }

    @Test
    @Order(2)
    public void testMilvusCollectionService() {
        if (milvusCollectionService == null) {
            logger.warn("Milvus集合服务未加载，跳过测试");
            return;
        }

        try {
            // 测试创建集合
            boolean created = milvusCollectionService.createCollection(testKnowledgeBaseId, 384);
            assertTrue(created || milvusCollectionService.hasCollection(testKnowledgeBaseId), 
                      "集合应该被成功创建或已存在");

            // 测试检查集合存在性
            boolean exists = milvusCollectionService.hasCollection(testKnowledgeBaseId);
            assertTrue(exists, "集合应该存在");

            // 测试加载集合
            boolean loaded = milvusCollectionService.loadCollection(testKnowledgeBaseId);
            assertTrue(loaded, "集合应该被成功加载");

            // 测试获取集合统计信息
            IMilvusCollectionService.CollectionStats stats = milvusCollectionService.getCollectionStats(testKnowledgeBaseId);
            assertNotNull(stats, "应该能获取集合统计信息");

            logger.info("Milvus集合服务测试通过，集合统计: {}", stats);
        } catch (Exception e) {
            logger.error("Milvus集合服务测试失败: {}", e.getMessage(), e);
            fail("Milvus集合服务测试失败: " + e.getMessage());
        }
    }

    @Test
    @Order(3)
    public void testMilvusEmbeddingStore() {
        if (customMilvusEmbeddingStore == null) {
            logger.warn("自定义Milvus存储未加载，跳过测试");
            return;
        }

        try {
            // 准备测试数据
            String[] testTexts = {
                "人工智能是计算机科学的一个分支",
                "机器学习是人工智能的核心技术",
                "深度学习是机器学习的一个子领域",
                "神经网络是深度学习的基础"
            };

            // 创建文本段落并添加元数据
            for (int i = 0; i < testTexts.length; i++) {
                TextSegment segment = TextSegment.from(testTexts[i]);
                segment.metadata().put("knowledgeBaseId", testKnowledgeBaseId.toString());
                segment.metadata().put("documentId", String.valueOf(i + 1));
                segment.metadata().put("documentName", "测试文档" + (i + 1));
                segment.metadata().put("documentType", "txt");

                // 计算嵌入向量
                Embedding embedding = embeddingModel.embed(testTexts[i]).content();

                // 添加到向量存储
                customMilvusEmbeddingStore.add(embedding, segment);
            }

            logger.info("成功添加 {} 个测试向量", testTexts.length);

            // 等待一段时间确保数据被索引
            Thread.sleep(2000);

            // 测试搜索功能
            String queryText = "什么是人工智能";
            Embedding queryEmbedding = embeddingModel.embed(queryText).content();

            EmbeddingSearchRequest searchRequest = EmbeddingSearchRequest.builder()
                    .queryEmbedding(queryEmbedding)
                    .maxResults(3)
                    .minScore(0.5)
                    .build();

            EmbeddingSearchResult<TextSegment> searchResult = 
                customMilvusEmbeddingStore.searchInKnowledgeBase(testKnowledgeBaseId, searchRequest);

            assertNotNull(searchResult, "搜索结果不应该为空");
            assertNotNull(searchResult.matches(), "搜索匹配结果不应该为空");
            assertTrue(searchResult.matches().size() > 0, "应该找到至少一个匹配结果");

            logger.info("搜索测试通过，找到 {} 个匹配结果", searchResult.matches().size());

            // 打印搜索结果
            for (int i = 0; i < searchResult.matches().size(); i++) {
                EmbeddingMatch<TextSegment> match = searchResult.matches().get(i);
                logger.info("匹配结果 {}: 相似度={}, 文本={}", 
                           i + 1, match.score(), match.embedded().text());
            }

        } catch (Exception e) {
            logger.error("Milvus向量存储测试失败: {}", e.getMessage(), e);
            fail("Milvus向量存储测试失败: " + e.getMessage());
        }
    }

    @Test
    @Order(4)
    public void testBatchOperations() {
        if (customMilvusEmbeddingStore == null) {
            logger.warn("自定义Milvus存储未加载，跳过批量操作测试");
            return;
        }

        try {
            // 准备批量测试数据
            List<String> batchTexts = Arrays.asList(
                "自然语言处理是人工智能的重要应用",
                "计算机视觉让机器能够理解图像",
                "语音识别技术不断发展进步",
                "推荐系统广泛应用于各个领域"
            );

            List<TextSegment> segments = batchTexts.stream()
                .map(text -> {
                    TextSegment segment = TextSegment.from(text);
                    segment.metadata().put("knowledgeBaseId", testKnowledgeBaseId.toString());
                    segment.metadata().put("batch", "true");
                    return segment;
                })
                .collect(java.util.stream.Collectors.toList());

            List<Embedding> embeddings = batchTexts.stream()
                .map(text -> embeddingModel.embed(text).content())
                .collect(java.util.stream.Collectors.toList());

            // 批量添加
            customMilvusEmbeddingStore.addAll(embeddings, segments);
            logger.info("批量添加 {} 个向量成功", batchTexts.size());

            // 等待索引
            Thread.sleep(2000);

            // 验证批量添加的数据
            String queryText = "自然语言处理";
            Embedding queryEmbedding = embeddingModel.embed(queryText).content();

            EmbeddingSearchRequest searchRequest = EmbeddingSearchRequest.builder()
                    .queryEmbedding(queryEmbedding)
                    .maxResults(5)
                    .minScore(0.3)
                    .build();

            EmbeddingSearchResult<TextSegment> searchResult = 
                customMilvusEmbeddingStore.searchInKnowledgeBase(testKnowledgeBaseId, searchRequest);

            assertTrue(searchResult.matches().size() > 0, "批量添加的数据应该能被搜索到");
            logger.info("批量操作测试通过");

        } catch (Exception e) {
            logger.error("批量操作测试失败: {}", e.getMessage(), e);
            fail("批量操作测试失败: " + e.getMessage());
        }
    }

    @Test
    @Order(5)
    public void testCleanup() {
        if (milvusCollectionService == null) {
            logger.warn("Milvus集合服务未加载，跳过清理测试");
            return;
        }

        try {
            // 清理测试数据
            boolean dropped = milvusCollectionService.dropCollection(testKnowledgeBaseId);
            logger.info("测试集合清理结果: {}", dropped ? "成功" : "失败或不存在");

            // 验证集合已被删除
            boolean exists = milvusCollectionService.hasCollection(testKnowledgeBaseId);
            assertFalse(exists, "测试集合应该已被删除");

            logger.info("清理测试通过");
        } catch (Exception e) {
            logger.error("清理测试失败: {}", e.getMessage(), e);
            // 清理失败不应该导致测试失败
        }
    }
}
